use crate::jup::jupiter_quote_poller;
use crate::logging::init_logging;
use chrono::Local;
use markets::cex::bybit::client::Client;
use markets::cex::bybit::config::Config;
use markets::cex::bybit::req_model::Tickers::LinearDelta;
use markets::cex::bybit::req_model::{Category, LinearTickerData, LinearTickerDeltaData, OrderHistory, OrderHistoryRequest, OrderRequest, OrderType, Side, SpotTickerData, Tickers};
use markets::cex::bybit::trade::Trader;
use markets::cex::bybit::util::generate_random_uid;
use markets::cex::bybit::ws::Stream;
use markets::dex::jupiter;
use markets::dex::jupiter::model::QuoteResponse::{Jupiter, Plan};
use markets::dex::jupiter::model::{DerivedPrice, QuotePlanResponse};
use markets::dex::jupiter::{price, quote, swap};
use std::borrow::Cow;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};
use chains::okx::swap::swap_quote::{get_swap_quote, SwapQuoteRequest};
use chains::solana::alt_router_example::example_alt_router_usage;
use chains::solana::router::{init_dex_router, router_create_token_account, router_meteora_dlmm_swap, router_multi_dex_linear_swap, router_orca_swap, router_pump_swap_sell_test, router_pump_swap_test, router_raydium_swap, router_raydium_to_pump_linear_swap, router_wrap_sol};
use crate::solana_account_converter::save;

pub mod logging;
mod jup;
mod solana_account_converter;

// 0.02 SOL
const SWAP_AMOUNT: u64 = 4 * 1_000_000_0;
// 套利机会阈值 (例如，百分比差异)  1.2%
const ARBITRAGE_THRESHOLD_PERCENT: f64 = 120.0 / 100.0;


pub async fn start() {
    let _guard = init_logging();
    info!("🚀🚀🚀 Starting application");

    let pairs: Vec<String> = vec![
        "SOLUSDT".to_string(),
        "POPCATUSDT".to_string(),
    ];

    save().expect("TODO: panic message");

    // let tt = vec![
    //     TokenPriceRequest {
    //         chain_index: "501".to_string(),
    //         token_contract_address: "GjGmkC899vNwUKjdMpiH4qV1ejhDGsBaojpwDGJzpump".to_string(),
    //     },
    //     TokenPriceRequest {
    //         chain_index: "501".to_string(),
    //         token_contract_address: "38PgzpJYu2HkiYvV8qePFakB8tuobPdGm2FFEn7Dpump".to_string(),
    //     },
    // ];
    //
    // let res = get_batch_quote_prices(tt)
    //     .await;


    // router_create_token_account();
    // router_wrap_sol();
    // init_dex_router().unwrap();
    // router_raydium_swap();
    // router_pump_swap_test();
    // router_pump_swap_sell_test();
    // router_meteora_dlmm_swap();
    // router_orca_swap();

    // router_multi_dex_linear_swap();
    // router_raydium_to_pump_linear_swap();

    // example_alt_router_usage().await.expect("TODO: panic message");

    // loop {
    //     okx_quote_swap().await;
    //     tokio::time::sleep(Duration::from_secs(1)).await;
    // }


    // chains::solana::dex_integrators::exec().await;
    // chains::solana::dex_integrators::wrap_sol().await;

    // --- 创建 Actor 通信 Channels ---
    let mut actor_senders_map: HashMap<String, mpsc::UnboundedSender<ActorMessage>> = HashMap::new();
    // 保存 actor task handles
    let mut actor_handles = vec![];

    for symbol in &pairs {
        let (sender, receiver) =
            mpsc::unbounded_channel::<ActorMessage>();
        actor_senders_map.insert(symbol.clone(), sender);

        // --- 启动每个交易对的 Actor ---
        let handle = tokio::spawn(pair_manager_actor(symbol.clone(), receiver));
        actor_handles.push(handle);
    }

    let shared_actor_senders = Arc::new(actor_senders_map);
    // --- 启动 Jupiter 价格轮询器 ---
    let jupiter_handle = tokio::spawn(
        // jupiter_poller(Arc::clone(&shared_actor_senders))
        jupiter_quote_poller(Arc::clone(&shared_actor_senders))
    );

    // --- 启动 CEX WebSocket 处理器 ---
    let cex_handle = tokio::spawn(
        bybit_websocket_handler(
            pairs.clone(),
            Arc::clone(&shared_actor_senders)
        )
    );

    tokio::select! {
        res = jupiter_handle => {
            error!("Jupiter Poller task finished unexpectedly: {:?}", res);
        }
        res = cex_handle => {
             error!("Bybit WebSocket Handler task finished unexpectedly: {:?}", res);
        }
    }
}


async fn okx_quote_swap() {
    let rr = SwapQuoteRequest {
        chain_index: "501".to_string(),
        chain_id: "501".to_string(),
        amount: SWAP_AMOUNT.to_string(),
        from_token_address: "So11111111111111111111111111111111111111112".to_string(),
        to_token_address: "B5boCskXEFb1RJJ9EqJNV3gt5fjhk85DeD7BgbLcpump".to_string(),
        dex_ids: None,
        direct_route: None,
        price_impact_protection_percentage: None,
        fee_percent: None,
    };

    let res = get_swap_quote(rr).await;

    if let Ok(p) = res {
        println!("Batch quote prices fetched successfully: {:?}", p);
        let quote = p.get(0).unwrap();
        let amount_out = &quote.quote_compare_list.get(0).unwrap().amount_out.parse::<f64>().unwrap_or(0.0);

        println!("Quote amount out: {}", amount_out);

        let amount_out = amount_out * 1_000_000_0.0; // 转换为最小单位
        let amount_in = amount_out.ceil() as u64;

        println!("Amount in for swap: {}", amount_in);

        let rr = SwapQuoteRequest {
            chain_index: "501".to_string(),
            chain_id: "501".to_string(),
            amount: amount_in.to_string(),
            from_token_address: "B5boCskXEFb1RJJ9EqJNV3gt5fjhk85DeD7BgbLcpump".to_string(),
            to_token_address: "So11111111111111111111111111111111111111112".to_string(),
            dex_ids: None,
            direct_route: None,
            price_impact_protection_percentage: None,
            fee_percent: None,
        };

        tokio::time::sleep(Duration::from_secs(1)).await; // 等待一秒钟

        let res = get_swap_quote(rr).await;

        if let Ok(p) = res {
            println!("Batch quote prices fetched successfully: {:?}", p);
            let quote = p.get(0).unwrap();
            let amount_out = &quote.quote_compare_list.get(0).unwrap().amount_out.parse::<f64>().unwrap_or(0.0);

            println!("Quote amount out: {}", amount_out);

            if amount_out > &0.4 {
                let percentage = (amount_out - 0.04) / 0.04 * 100.0;
                println!("execute swap, percentage: {}", percentage);
            }

        } else {
            error!("Failed to fetch batch quote prices: {:?}", res);
            return;
        }

    } else {
        error!("Failed to fetch batch quote prices: {:?}", res);
        return;
    }
}


// Bybit WebSocket 处理器
async fn bybit_websocket_handler(
    pairs_to_subscribe: Vec<String>,
    actor_senders: Arc<HashMap<String, mpsc::UnboundedSender<ActorMessage>>>,
) {
    debug!("Bybit WebSocket Handler started for pairs: {:?}", pairs_to_subscribe);

    // 初始等待时间
    let mut backoff_duration = Duration::from_secs(1);
    // 最大等待时间
    const MAX_BACKOFF: Duration = Duration::from_secs(60);

    loop {
        info!("Starting Bybit WebSocket handler...");
        let client = Client::new(None, None, Config::default().ws_endpoint.to_string());
        let ws = Stream::new(client);
        let (tx, mut rx) = mpsc::unbounded_channel::<>();

        let pairs = pairs_to_subscribe.clone();
        let mut subscribe_task = tokio::spawn(async move {
            let ws_sub = &ws.ws_tickers(
                pairs.iter().map(AsRef::as_ref).collect(),
                Category::Spot, tx
            ).await;

            if let Err(e) = ws_sub {
                error!("Failed to subscribe to Tickers: {:?}", e);
                return;
            } else {
                info!("成功订阅 Bybit WebSocket");
                // 重置退避时间
                backoff_duration = Duration::from_secs(1);
                debug!("Subscribed to Tickers successfully");
            }
        });

        // 循环处理来自 WebSocket 的消息
        'message_loop: loop {
            tokio::select! {
                Some(msg) = rx.recv() => {
                    match msg {
                        Tickers::Linear(tk) => {
                            if let Some(sender) = actor_senders.get(&tk.symbol) {
                                if let Err(e) = sender.send(ActorMessage::CexUpdate(tk)) {
                                    error!("Failed to send Bybit price for: {}. Actor might have stopped.", e);
                                }
                            }
                        }
                        Tickers::Spot(tk) => {
                            debug!("Bybit Spot Ticker: {}==>{}", tk.symbol, tk.last_price);
                            if &tk.symbol == "SOLUSDT" {
                                for (_, sender) in actor_senders.iter() {
                                    if let Err(e) = sender.send(ActorMessage::CexSpotMidUpdate(tk.clone())) {
                                        error!("Failed to send Bybit Spot price for: {}. Actor might have stopped.", e);
                                    }
                                }
                            } else {
                                if let Some(sender) = actor_senders.get(&tk.symbol) {
                                    if let Err(e) = sender.send(ActorMessage::CexSpotUpdate(tk)) {
                                        error!("Failed to send Bybit Spot price for: {}. Actor might have stopped.", e);
                                    }
                                }
                            }
                        }
                        LinearDelta(tk) => {
                            if &tk.symbol == "SOLUSDT" {
                                if let Some(sender) = actor_senders.get(&tk.symbol) {
                                   if let Err(e) = sender.send(ActorMessage::CexMidDeltaUpdate(tk)) {
                                        error!("Failed to send Bybit Mid price for: {}. Actor might have stopped.", e);
                                    }
                                }
                            } else {
                                if let Some(sender) = actor_senders.get(&tk.symbol) {
                                    if let Err(e) = sender.send(ActorMessage::CexDeltaUpdate(tk)) {
                                        error!("Failed to send Bybit price for: {}. Actor might have stopped.", e);
                                    }
                                }
                            }
                        }
                    }
                },
                 // _ = tokio::signal::ctrl_c() => { // 允许通过 Ctrl+C 优雅停止
                 //     info!("CEX WebSocket handler received shutdown signal.");
                 //     break;
                 // }
                // 可以监控 subscribe_task 是否意外结束
                 res = &mut subscribe_task => {
                    error!("WebSocket subscription task finished unexpectedly: {:?}", res);
                    break 'message_loop; // 退出处理循环
                }
            }
        }

        tokio::time::sleep(backoff_duration).await;
        backoff_duration = std::cmp::min(backoff_duration * 2, MAX_BACKOFF);
    }

    info!("Bybit WebSocket Handler stopped.");
}



// 发送给交易对 Actor 的消息类型
#[derive(Debug)]
enum ActorMessage {
    JupiterUpdate(DerivedPrice),
    JupiterQuoteUpdate(QuotePlanResponse),
    CexUpdate(LinearTickerData),
    CexDeltaUpdate(LinearTickerDeltaData),
    CexSpotUpdate(SpotTickerData),
    CexSpotMidUpdate(SpotTickerData),
    CexMidUpdate(LinearTickerData),
    CexMidDeltaUpdate(LinearTickerDeltaData),
}



// 交易对管理器 Actor
async fn pair_manager_actor(
    symbol: String,
    mut receiver: mpsc::UnboundedReceiver<ActorMessage>,
) {
    info!("[{}] Actor started", symbol);
    let mut jupiter_price: Option<QuotePlanResponse> = None;
    let mut bybit_price: Option<LinearTickerData> = None;
    let mut bybit_mid_price: Option<LinearTickerData> = None;

    let mut bybit_spot_price: Option<SpotTickerData> = None;
    let mut bybit_spot_mid_price: Option<SpotTickerData> = None;

    let mut last_quote_swap_time: Option<Instant> = None;
    const QUOTE_SWAP_COOLDOWN: Duration = Duration::from_secs(60);

    while let Some(message) = receiver.recv().await {
        // info!("[{}] Received message: {:?}", symbol, message); // 调试日志，可能很吵
        match message {
            ActorMessage::JupiterUpdate(price) => {
                // jupiter_price = Some(price);
            }
            ActorMessage::JupiterQuoteUpdate(price) => {
                jupiter_price = Some(price);
            }
            ActorMessage::CexUpdate(price) => {
                bybit_price = Some(price);
            }
            ActorMessage::CexDeltaUpdate(delta) => {
                if let Some(price) = bybit_price.as_mut() {
                    merge_linear_tickers(price, &delta);
                }
            }
            ActorMessage::CexMidUpdate(price) => {
                bybit_mid_price = Some(price);
            }
            ActorMessage::CexMidDeltaUpdate(delta) => {
                if let Some(price) = bybit_mid_price.as_mut() {
                    merge_linear_tickers(price, &delta);
                }
            }
            ActorMessage::CexSpotUpdate(price) => {
                bybit_spot_price = Some(price);
            }
            ActorMessage::CexSpotMidUpdate(price) => {
                bybit_spot_mid_price = Some(price);
            }
        }

        // 收到任何一方的新价格后都尝试比较
        let now = Instant::now();
        let mut should_call_swap = true;
        if let Some(last_call) = last_quote_swap_time {
            if now.duration_since(last_call) < QUOTE_SWAP_COOLDOWN {
                should_call_swap = false;
            }
        } else {
            last_quote_swap_time = Some(now);
        }

        if should_call_swap {
            compare_prices(&symbol, jupiter_price.clone(), &bybit_spot_price, &bybit_spot_mid_price, &mut last_quote_swap_time);
        }
    }
    info!("[{}] Actor stopped", symbol);
}


// 价格比较与套利检查逻辑
fn compare_prices(
    symbol: &str,
    jupiter_price_opt: Option<QuotePlanResponse>,
    bybit_price_opt: &Option<SpotTickerData>,
    bybit_mid_price_opt: &Option<SpotTickerData>,
    last_swap_time: &mut Option<Instant>
) {
    if let (Some(jup_p), Some(bybit_p), Some(bybit_mid_p))
        = (jupiter_price_opt, bybit_price_opt, bybit_mid_price_opt) {

        let tk_address = &jup_p.output_mint;
        let out_num: u64 = jup_p.route_plan.iter()
            .filter(|x| x.swap_info.output_mint == *tk_address)
            .map(|x| x.swap_info.out_amount.parse::<u64>().unwrap_or(0))
            .sum();
        let jup_quote_num = out_num as f64 / 1000000000.0;


        let cex_val = bybit_p.last_price.parse::<f64>().unwrap_or(0.0);
        let mid_val = bybit_mid_p.last_price.parse::<f64>().unwrap_or(0.0);
        let cex_bid_price = bybit_p.last_price.parse::<f64>().unwrap_or(0.0);

        if jup_quote_num <= 0.0 || cex_val <= 0.0 || mid_val <= 0.0 {
            warn!("【{}】 Invalid price(s) for comparison: Jupiter={}, CEX={}", symbol, jup_quote_num, cex_val);
            // 避免除零或无效比较
            return;
        }

        let cex_swap_mid = jup_quote_num * cex_bid_price / mid_val;
        let profit = (cex_swap_mid - 0.04) * mid_val;
        let profit_percent = profit / (0.04 * mid_val) * 100.0;

        // 详细日志，观察价格变动
        info!(
            "【{}】 Prices updated: Jupiter: {:.4}, CEX price: {:.4}, SOL price: {:.4}, \
                SOL swap out: {:.8}, Profit: {:.4}, Spread: {:.4}%",
            symbol, jup_quote_num, cex_val, mid_val, cex_swap_mid, profit, profit_percent
        );

        if profit_percent > ARBITRAGE_THRESHOLD_PERCENT {
            info!("【{}】 Buy on Jupiter, Sell on CEX, Jupiter swap num: {:.4}, CEX bid: {}, \
                sol bid: {}, Profit Percent: {:.4}%, profit: {:.4}",
                    symbol, jup_quote_num, bybit_p.last_price, bybit_mid_p.last_price, profit_percent, profit);

            let now = Instant::now();
            last_swap_time.replace(now);

            // 在这里触发实际的套利执行逻辑 (例如发送交易指令到另一个 Actor)
            let sy = symbol.to_string();
            tokio::spawn(async move {
                trigger_arbitrage_execution(sy, jup_quote_num, cex_bid_price, jup_p.clone(), mid_val).await;
            });

            let message = format!(
                "【{}】 Buy Jupiter 5 SOL => {:.4}, \
                        Sell CEX: {:.4} => {:.8}, Profit Percent: {:.4}%, profit: {:.8} \\n\\n时间\\n{}",
                symbol, jup_quote_num, jup_quote_num, cex_swap_mid,  profit_percent, profit,  Local::now().format("%Y-%m-%d %H:%M"));

            tokio::spawn(async move {
                info!("sending message");
                let res = notify::lark::send_message(&message, notify::lark::OPEN_ID).await;
                if let Err(e) = res {
                    error!("Failed to send message: {}", e);
                }
            });
        }

    } else {
        // info!("[{}] Waiting for both prices (Jupiter: {}, CEX: {})",
        //        symbol, jupiter_price_opt.is_some(), cex_price_opt.is_some());
    }
}


async fn create_order(price: f64, qty: f64, symbol: &str, side: Side, order_type: OrderType, client: Client) -> Result<String, String> {
    let req = OrderRequest {
        category: Category::Spot,
        symbol: Cow::Borrowed(symbol),
        side,
        qty,
        order_type,
        position_idx: Some(0),
        order_link_id: Some(generate_random_uid(36).into()),
        price: Some(price),
        ..Default::default()
    };

    info!("Creating order: {:?}", req);

    let trader = Trader {
        client,
        recv_window: 5000,
    };

    let res = trader.place_custom_order(req).await;
    match res {
        Ok(order) => {
            info!("Order placed successfully: {:?}", order);
            Ok(order.result.order_id.unwrap())
        }
        Err(e) => {
            error!("Failed to place order: {}", e);
            Err(format!("Failed to place order: {}", e))
        }
    }
}

async fn find_order(order_id: &str,client: Client) -> Result<OrderHistory, String> {
    let req = OrderHistoryRequest {
        category: Category::Spot,
        symbol: Some(Cow::Borrowed("POPCATUSDT")),
        order_id: Some(Cow::Borrowed(order_id)),
        ..Default::default()
    };
    let trader = Trader {
        client,
        recv_window: 5000,
    };
    let history = trader.get_order_history(req).await;
    match history {
        Ok(order) => {
            info!("Order history: {:?}", order);
            if order.ret_code != 0 {
                error!("Failed to get order history: {}", order.ret_msg);
                Err(format!("Failed to get order history: {}", order.ret_msg))
            } else {
                Ok(order.result)
            }
        }
        Err(e) => {
            error!("Failed to get order history: {}", e);
            Err(format!("Failed to get order history: {}", e))
        }
    }
}


fn merge_linear_tickers(cached: &mut LinearTickerData, delta: &LinearTickerDeltaData) {
    // Merge the delta into the cached ticker
    // Helper function to update from Option<String>
    fn update_if_present<T: AsRef<str>>(cached_value: &mut String, delta_value: Option<T>) {
        if let Some(value) = delta_value {
            *cached_value = value.as_ref().to_owned();
        }
    }

    update_if_present(&mut cached.tick_direction, delta.tick_direction.as_ref());
    update_if_present(&mut cached.price_24h_pcnt, delta.price_24h_pcnt.as_ref());
    update_if_present(&mut cached.last_price, delta.last_price.as_ref());
    update_if_present(&mut cached.prev_price_24h, delta.prev_price_24h.as_ref());
    update_if_present(&mut cached.high_price_24h, delta.high_price_24h.as_ref());
    update_if_present(&mut cached.low_price_24h, delta.low_price_24h.as_ref());
    update_if_present(&mut cached.prev_price_1h, delta.prev_price_1h.as_ref());
    update_if_present(&mut cached.mark_price, delta.mark_price.as_ref());
    update_if_present(&mut cached.index_price, delta.index_price.as_ref());
    update_if_present(&mut cached.open_interest, delta.open_interest.as_ref());
    update_if_present(&mut cached.open_interest_value, delta.open_interest_value.as_ref());
    update_if_present(&mut cached.turnover_24h, delta.turnover_24h.as_ref());
    update_if_present(&mut cached.volume_24h, delta.volume_24h.as_ref());
    update_if_present(&mut cached.next_funding_time, delta.next_funding_time.as_ref());
    update_if_present(&mut cached.funding_rate, delta.funding_rate.as_ref());
    update_if_present(&mut cached.bid_price, delta.bid_price.as_ref());
    update_if_present(&mut cached.bid_size, delta.bid_size.as_ref());
    update_if_present(&mut cached.ask_price, delta.ask_price.as_ref());
    update_if_present(&mut cached.ask_size, delta.ask_size.as_ref());
    update_if_present(&mut cached.bid_price, delta.bid_price.as_ref());
    update_if_present(&mut cached.bid_size, delta.bid_size.as_ref());
    update_if_present(&mut cached.ask_price, delta.ask_price.as_ref());
    update_if_present(&mut cached.ask_size, delta.ask_size.as_ref());
}

// 触发套利执行逻辑
async fn trigger_arbitrage_execution(symbol: String, num: f64, cex_price: f64, jupiter_price: QuotePlanResponse, cex_mid_price: f64) {
    // 这里可以添加实际的套利执行逻辑
    info!("Triggering arbitrage execution for {}", symbol);
    let user = "GFi3B9st6Ni6AUqLJ3WGrDEJqRLHG4HGQmqGWeYEaRk5".to_string();
    let start_time = Instant::now();
    let tx = swap(user, jupiter_price).await;
    info!("exe jup swap quote: {}", start_time.elapsed().as_millis());

    if let Err(e) = tx {
        error!("Error executing swap: {}", e);
        return;
    }

    let send_tx = chains::solana::exec_jup_swap(tx.unwrap().swap_transaction).await;
    info!("exe jup swap: {}", start_time.elapsed().as_millis());

    if let Err(e) = send_tx {
        error!("Error sending transaction: {}", e);
        return;
    }


    let client = Client::new(Some("Ez7ZmzEcKYYHuUt2EJ".to_string()),
                             Some("2iMyHTsgr4DwdTxCJSLTNOktLFmcRXETD4Tz".to_string()),
                             Config::default().rest_api_endpoint.to_string());

    // num 保留两位小数
    let num = format!("{:.2}", num).parse::<f64>().unwrap_or(0.0);
    if num <= 0.0 {
        error!("Invalid quantity for order: {}", num);
        return;
    }
    let order_id = create_order(0.22, num, "POPCATUSDT", Side::Sell,
                                OrderType::Market, client.clone()).await;
    info!("exe cex sell: {}", start_time.elapsed().as_millis());

    if let Err(e) = order_id {
        error!("Error creating order: {}", e);
        return;
    }

    let order_id = order_id.unwrap();
    let order = find_order(&order_id, client.clone()).await;
    info!("exe cex find order: {}", start_time.elapsed().as_millis());
    if let Err(e) = order {
        error!("Error finding order: {}", e);
        return;
    }

    let sol_qty = format!("{:.3}", (num * cex_price) / cex_mid_price ).parse::<f64>().unwrap_or(0.0);

    info!("Buy SOL bb qty: {}", sol_qty);

    let result = create_order(cex_mid_price, sol_qty, "SOLUSDT", Side::Buy,
                              OrderType::Limit, client.clone()).await;
    info!("exe cex buy: {}", start_time.elapsed().as_millis());
    if let Err(e) = result {
        error!("Error creating order: {}", e);
        return;
    }
    info!("Buy SOL order success");
}


#[allow(dead_code)]
async fn test_swap() {
    let sol = "So11111111111111111111111111111111111111112";
    let symbol_address = "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr";
    let symbol = "POPCATUSDT";

    let quote = jupiter::quote(sol.to_string(), symbol_address.to_string(), SWAP_AMOUNT)
        .await;

    if let Err(e) = quote {
        error!("Failed to get Jupiter quote: {}", e);
        tokio::time::sleep(Duration::from_secs(30)).await;
        return;;
    }

    match quote.unwrap() {
        Plan(q) => {
            info!("Triggering arbitrage execution for {}", symbol);
            let user = "GFi3B9st6Ni6AUqLJ3WGrDEJqRLHG4HGQmqGWeYEaRk5".to_string();
            let start_time = Instant::now();
            let tx = swap(user, q).await;
            info!("exe jup swap quote: {}", start_time.elapsed().as_millis());

            if let Err(e) = tx {
                error!("Error executing swap: {}", e);
                return;
            }

            let send_tx = chains::solana::exec_jup_swap(tx.unwrap().swap_transaction).await;
            info!("exe jup swap: {}", start_time.elapsed().as_millis());

            if let Err(e) = send_tx {
                error!("Error sending transaction: {}", e);
                return;
            }
        }
        _ => {}
    }
}


#[allow(dead_code)]
async fn test_get_cex_order() {
    let order_id = "1928601637988074240";
    let client = Client::new(Some("Ez7ZmzEcKYYHuUt2EJ".to_string()),
                             Some("2iMyHTsgr4DwdTxCJSLTNOktLFmcRXETD4Tz".to_string()),
                             Config::default().rest_api_endpoint.to_string());
    let order = find_order(&order_id, client.clone()).await;

    if let Err(e) = order {
        error!("Error finding order: {}", e);
        return;
    }

    let order = order.unwrap();
    if let Some(od) = order.list.first() {
        println!("Order ID: {}, {:?}", od.order_id, od);
    }
}
