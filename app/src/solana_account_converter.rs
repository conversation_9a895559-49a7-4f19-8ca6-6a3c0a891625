use serde_json::Value;
use std::fs;
use base64;

pub fn save() -> Result<(), Box<dyn std::error::Error>> {
    // Read the JSON data from command line argument or use the provided data
    let json_data = std::env::args().nth(1).unwrap_or_else(|| {
        r#"{"version":{"type":"u64","data":"1"},"lastUpdate":{"type":{"defined":"LastUpdate"},"data":{"slot":{"type":"u64","data":"*********"},"stale":{"type":"u8","data":0},"priceStatus":{"type":"u8","data":63},"placeholder":{"type":{"array":["u8",6]},"data":[0,0,0,0,0,0]}}},"lendingMarket":{"type":"publicKey","data":"7u3HeHxYDLhnCoErrtycNokbQYbWGzLs6JSDqGAv5PfF"},"farmCollateral":{"type":"publicKey","data":"JAvnB9AKtgPsTEoKmn24Bq64UMoYcrtWtq42HHBdsPkh"},"farmDebt":{"type":"publicKey","data":"11111111111111111111111111111111"},"liquidity":{"type":{"defined":"ReserveLiquidity"},"data":{"mintPubkey":{"type":"publicKey","data":"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"},"supplyVault":{"type":"publicKey","data":"Bgq7trRgVMeq33yt235zM2onQ4bRDBsY5EWiTetF4qw6"},"feeVault":{"type":"publicKey","data":"BbDUrk1bVtSixgQsPLBJFZEF7mwGstnD5joA1WzYvYFX"},"availableAmount":{"type":"u64","data":"150899649385243"},"borrowedAmountSf":{"type":"u128","data":"4332483024651737796640540556802311"},"marketPriceSf":{"type":"u128","data":"1152771555635957809"},"marketPriceLastUpdatedTs":{"type":"u64","data":"1756616305"},"mintDecimals":{"type":"u64","data":"6"},"depositLimitCrossedTimestamp":{"type":"u64","data":"0"},"borrowLimitCrossedTimestamp":{"type":"u64","data":"0"},"cumulativeBorrowRateBsf":{"type":{"defined":"BigFractionBytes"},"data":{"value":{"type":{"array":["u64",4]},"data":["1412007458627000448","0","0","0"]},"padding":{"type":{"array":["u64",2]},"data":["0","0"]}}},"accumulatedProtocolFeesSf":{"type":"u128","data":"1199197258857674967611388911068"},"accumulatedReferrerFeesSf":{"type":"u128","data":"0"},"pendingReferrerFeesSf":{"type":"u128","data":"0"},"absoluteReferralRateSf":{"type":"u128","data":"0"},"tokenProgram":{"type":"publicKey","data":"TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"},"padding2":{"type":{"array":["u64",51]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]},"padding3":{"type":{"array":["u128",32]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]}}},"reserveLiquidityPadding":{"type":{"array":["u64",150]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]},"collateral":{"type":{"defined":"ReserveCollateral"},"data":{"mintPubkey":{"type":"publicKey","data":"B8V6WVjPxW1UGwVDfxH2d2r8SyT4cqn7dQRK6XneVa7D"},"mintTotalSupply":{"type":"u64","data":"455968423545172"},"supplyVault":{"type":"publicKey","data":"3DzjXRfxRm6iejfyyMynR4tScddaanrePJ1NJU2XnPPL"},"padding1":{"type":{"array":["u128",32]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]},"padding2":{"type":{"array":["u128",32]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]}}},"reserveCollateralPadding":{"type":{"array":["u64",150]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]},"config":{"type":{"defined":"ReserveConfig"},"data":{"status":{"type":"u8","data":0},"assetTier":{"type":"u8","data":0},"hostFixedInterestRateBps":{"type":"u16","data":"0"},"reserved2":{"type":{"array":["u8",9]},"data":[0,0,0,0,0,0,0,0,0]},"protocolOrderExecutionFeePct":{"type":"u8","data":0},"protocolTakeRatePct":{"type":"u8","data":10},"protocolLiquidationFeePct":{"type":"u8","data":50},"loanToValuePct":{"type":"u8","data":80},"liquidationThresholdPct":{"type":"u8","data":90},"minLiquidationBonusBps":{"type":"u16","data":"500"},"maxLiquidationBonusBps":{"type":"u16","data":"1000"},"badDebtLiquidationBonusBps":{"type":"u16","data":"99"},"deleveragingMarginCallPeriodSecs":{"type":"u64","data":"604800"},"deleveragingThresholdDecreaseBpsPerDay":{"type":"u64","data":"7200"},"fees":{"type":{"defined":"ReserveFees"},"data":{"borrowFeeSf":{"type":"u64","data":"0"},"flashLoanFeeSf":{"type":"u64","data":"11529215046068"},"padding":{"type":{"array":["u8",8]},"data":[0,0,0,0,0,0,0,0]}}},"borrowRateCurve":{"type":{"defined":"BorrowRateCurve"},"data":{"points":{"type":{"array":[{"defined":"CurvePoint"},11]},"data":[{"utilizationRateBps":0,"borrowRateBps":0},{"utilizationRateBps":9500,"borrowRateBps":475},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000},{"utilizationRateBps":10000,"borrowRateBps":3000}]}}},"borrowFactorPct":{"type":"u64","data":"100"},"depositLimit":{"type":"u64","data":"640000000000000"},"borrowLimit":{"type":"u64","data":"560000000000000"},"tokenInfo":{"type":{"defined":"TokenInfo"},"data":{"name":{"type":{"array":["u8",32]},"data":[85,83,68,67,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},"heuristic":{"type":{"defined":"PriceHeuristic"},"data":{"lower":{"type":"u64","data":"90"},"upper":{"type":"u64","data":"105"},"exp":{"type":"u64","data":"2"}}},"maxTwapDivergenceBps":{"type":"u64","data":"300"},"maxAgePriceSeconds":{"type":"u64","data":"180"},"maxAgeTwapSeconds":{"type":"u64","data":"240"},"scopeConfiguration":{"type":{"defined":"ScopeConfiguration"},"data":{"priceFeed":{"type":"publicKey","data":"3NJYftD5sjVfxSnUdZ1wVML8f3aC6mp1CXCL6L7TnU8C"},"priceChain":{"type":{"array":["u16",4]},"data":[20,65535,65535,65535]},"twapChain":{"type":{"array":["u16",4]},"data":[62,65535,65535,65535]}}},"switchboardConfiguration":{"type":{"defined":"SwitchboardConfiguration"},"data":{"priceAggregator":{"type":"publicKey","data":"11111111111111111111111111111111"},"twapAggregator":{"type":"publicKey","data":"11111111111111111111111111111111"}}},"pythConfiguration":{"type":{"defined":"PythConfiguration"},"data":{"price":{"type":"publicKey","data":"11111111111111111111111111111111"}}},"blockPriceUsage":{"type":"u8","data":0},"reserved":{"type":{"array":["u8",7]},"data":[0,0,0,0,0,0,0]},"padding":{"type":{"array":["u64",19]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]}}},"depositWithdrawalCap":{"type":{"defined":"WithdrawalCaps"},"data":{"configCapacity":{"type":"i64","data":"200000000000000"},"currentTotal":{"type":"i64","data":"3609625930082"},"lastIntervalStartTimestamp":{"type":"u64","data":"1756552234"},"configIntervalLengthSeconds":{"type":"u64","data":"86400"}}},"debtWithdrawalCap":{"type":{"defined":"WithdrawalCaps"},"data":{"configCapacity":{"type":"i64","data":"200000000000000"},"currentTotal":{"type":"i64","data":"-109373170836"},"lastIntervalStartTimestamp":{"type":"u64","data":"1756566618"},"configIntervalLengthSeconds":{"type":"u64","data":"86400"}}},"elevationGroups":{"type":{"array":["u8",20]},"data":[1,3,6,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},"disableUsageAsCollOutsideEmode":{"type":"u8","data":0},"utilizationLimitBlockBorrowingAbovePct":{"type":"u8","data":0},"autodeleverageEnabled":{"type":"u8","data":0},"reserved1":{"type":{"array":["u8",1]},"data":[0]},"borrowLimitOutsideElevationGroup":{"type":"u64","data":"18446744073709551615"},"borrowLimitAgainstThisCollateralInElevationGroup":{"type":{"array":["u64",32]},"data":["0","0","18446744073709551615","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]},"deleveragingBonusIncreaseBpsPerDay":{"type":"u64","data":"0"}}},"configPadding":{"type":{"array":["u64",116]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]},"borrowedAmountOutsideElevationGroup":{"type":"u64","data":"374157146081220"},"borrowedAmountsAgainstThisReserveInElevationGroups":{"type":{"array":["u64",32]},"data":["0","0","18510795996","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]},"padding":{"type":{"array":["u64",207]},"data":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0"]}}"#.to_string()
    });

    // Parse the JSON
    let mut data: Value = serde_json::from_str(&*json_data)?;

    // Navigate to the slot field and change it to 155
    if let Some(last_update) = data.get_mut("lastUpdate") {
        if let Some(data_obj) = last_update.get_mut("data") {
            if let Some(slot) = data_obj.get_mut("slot") {
                if let Some(slot_data) = slot.get_mut("data") {
                    *slot_data = Value::String("155".to_string());
                    println!("Successfully changed slot from ********* to 155");
                }
            }
        }
    }

    // Convert to RAW format for Solana test validator
    let raw_data = convert_to_raw_format(&data)?;

    // Save the modified JSON
    let modified_json = serde_json::to_string_pretty(&data)?;
    fs::write("modified_account.json", modified_json)?;
    println!("Modified JSON saved to modified_account.json");

    // Also save as hex string for easy loading
    let hex_string = raw_data.iter()
        .map(|b| format!("{:02x}", b))
        .collect::<String>();
    fs::write("account_raw.hex", hex_string)?;
    println!("RAW format as hex saved to account_raw.hex");

    // Convert to base64 format (like the expected output)
    let base64_string = base64::encode(&raw_data);
    fs::write("account_raw.base64", base64_string)?;
    println!("RAW format as base64 saved to account_raw.base64");

    Ok(())
}

fn convert_to_raw_format(data: &Value) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    let mut raw_data = Vec::new();

    // This is a simplified conversion - in practice, you'd need to know the exact
    // binary layout of the Solana account structure
    serialize_value(data, &mut raw_data)?;

    Ok(raw_data)
}

fn serialize_value(value: &Value, buffer: &mut Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {
    match value {
        Value::Object(obj) => {
            if let (Some(type_info), Some(data)) = (obj.get("type"), obj.get("data")) {
                serialize_typed_data(type_info, data, buffer)?;
            } else {
                // Regular object - serialize all fields
                for (_, v) in obj {
                    serialize_value(v, buffer)?;
                }
            }
        }
        _ => {
            // For other value types, we'd need specific handling
        }
    }
    Ok(())
}

fn serialize_typed_data(type_info: &Value, data: &Value, buffer: &mut Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {
    match type_info {
        Value::String(type_str) => {
            match type_str.as_str() {
                "u8" => {
                    if let Value::Number(n) = data {
                        buffer.push(n.as_u64().unwrap_or(0) as u8);
                    }
                }
                "u16" => {
                    if let Value::String(s) = data {
                        let val: u16 = s.parse().unwrap_or(0);
                        buffer.extend_from_slice(&val.to_le_bytes());
                    }
                }
                "u64" => {
                    if let Value::String(s) = data {
                        let val: u64 = s.parse().unwrap_or(0);
                        buffer.extend_from_slice(&val.to_le_bytes());
                    }
                }
                "u128" => {
                    if let Value::String(s) = data {
                        let val: u128 = s.parse().unwrap_or(0);
                        buffer.extend_from_slice(&val.to_le_bytes());
                    }
                }
                "i64" => {
                    if let Value::String(s) = data {
                        let val: i64 = s.parse().unwrap_or(0);
                        buffer.extend_from_slice(&val.to_le_bytes());
                    }
                }
                "publicKey" => {
                    if let Value::String(s) = data {
                        // Convert base58 public key to 32 bytes
                        match bs58::decode(s).into_vec() {
                            Ok(bytes) if bytes.len() == 32 => {
                                buffer.extend_from_slice(&bytes);
                            }
                            _ => {
                                // If decoding fails or wrong length, use zeros
                                buffer.extend_from_slice(&[0u8; 32]);
                            }
                        }
                    }
                }
                _ => {
                    // Handle other types or nested structures
                    serialize_value(data, buffer)?;
                }
            }
        }
        Value::Object(type_obj) => {
            if let Some(Value::Array(array_def)) = type_obj.get("array") {
                if let Value::Array(arr) = data {
                    // Handle typed arrays
                    if array_def.len() >= 2 {
                        let element_type = &array_def[0];
                        for item in arr {
                            serialize_typed_data(element_type, item, buffer)?;
                        }
                    }
                }
            } else if type_obj.get("defined").is_some() {
                // Handle defined types
                serialize_value(data, buffer)?;
            }
        }
        _ => {}
    }
    Ok(())
}
